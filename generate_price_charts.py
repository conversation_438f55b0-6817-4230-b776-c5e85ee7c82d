"""
<PERSON><PERSON>t to generate price charts for all input data files

This script:
1. Reads all CSV files from the 'input' directory
2. Generates price charts for each file
3. Saves the charts in a 'charts' directory
"""

import os
import glob
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from dexapp.utils.data_processor import DataProcessor


def main():
    """
    Main function to generate price charts
    """
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description='Generate price charts for all input data files')
    parser.add_argument('--advanced', action='store_true', help='Generate advanced charts with technical indicators')
    parser.add_argument('--output_dir', type=str, default='charts', help='Directory to save charts')
    args = parser.parse_args()

    # Create charts directory if it doesn't exist
    charts_dir = args.output_dir
    os.makedirs(charts_dir, exist_ok=True)

    # Get all CSV files in the input directory
    input_dir = 'input'
    csv_files = glob.glob(os.path.join(input_dir, "*.csv"))

    if not csv_files:
        print(f"No CSV files found in {input_dir} directory.")
        return

    print(f"Found {len(csv_files)} CSV files in {input_dir} directory.")

    # Initialize data processor
    data_processor = DataProcessor(data_dir=input_dir)

    # Process each file
    for file_path in csv_files:
        file_name = os.path.basename(file_path)
        print(f"Processing {file_name}...")

        try:
            # Load data
            df = data_processor.load_data(file_name)

            # Generate basic price chart
            generate_price_chart(df, file_name, charts_dir)

            # Generate advanced chart if requested
            if args.advanced:
                generate_advanced_chart(df, file_name, charts_dir)

        except Exception as e:
            print(f"Error processing {file_name}: {e}")


def generate_price_chart(df, file_name, charts_dir):
    """
    Generate a price chart for the given DataFrame

    Args:
        df: DataFrame with price data
        file_name: Name of the data file
        charts_dir: Directory to save charts
    """
    # Create figure
    plt.figure(figsize=(15, 8))

    # Plot price
    plt.plot(df.index, df['c'], label='Close Price', color='blue')

    # Add volume as bar chart on secondary y-axis
    ax1 = plt.gca()
    ax2 = ax1.twinx()
    ax2.bar(df.index, df['v'], alpha=0.3, color='gray', label='Volume')
    ax2.set_ylabel('Volume')

    # Format x-axis dates
    plt.gcf().autofmt_xdate()
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))

    # Add grid and labels
    ax1.grid(True, alpha=0.3)
    ax1.set_title(f'Price Chart - {file_name}')
    ax1.set_xlabel('Date')
    ax1.set_ylabel('Price')

    # Add legend
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

    # Calculate and display statistics
    if len(df) > 0:
        min_price = df['c'].min()
        max_price = df['c'].max()
        avg_price = df['c'].mean()
        start_price = df['c'].iloc[0]
        end_price = df['c'].iloc[-1]
        price_change = (end_price - start_price) / start_price * 100

        stats_text = (
            f"Min: {min_price:.8f}\n"
            f"Max: {max_price:.8f}\n"
            f"Avg: {avg_price:.8f}\n"
            f"Start: {start_price:.8f}\n"
            f"End: {end_price:.8f}\n"
            f"Change: {price_change:.2f}%"
        )

        # Add stats text box
        plt.figtext(0.02, 0.02, stats_text, bbox=dict(facecolor='white', alpha=0.8))

    # Save chart
    output_file = os.path.join(charts_dir, f"{os.path.splitext(file_name)[0]}_chart.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Saved chart to {output_file}")

    # Close figure to free memory
    plt.close()


def generate_advanced_chart(df, file_name, charts_dir):
    """
    Generate an advanced chart with technical indicators

    Args:
        df: DataFrame with price data
        file_name: Name of the data file
        charts_dir: Directory to save charts
    """
    # Add technical indicators if needed
    from dexapp.indicators.technical import TechnicalIndicators

    # Preprocess data
    df_processed = df.copy()

    # Ensure numeric columns
    for col in ['o', 'h', 'l', 'c', 'v']:
        if col in df_processed.columns:
            df_processed[col] = pd.to_numeric(df_processed[col])

    # Add technical indicators
    try:
        df_with_indicators = TechnicalIndicators.add_all_indicators(df_processed)
        df_with_indicators.dropna(inplace=True)

        # Create figure with subplots
        fig, axs = plt.subplots(3, 1, figsize=(15, 12), sharex=True, gridspec_kw={'height_ratios': [3, 1, 1]})

        # Plot 1: Price and Bollinger Bands
        axs[0].plot(df_with_indicators.index, df_with_indicators['c'], label='Close Price', color='blue')
        if 'bb_high' in df_with_indicators.columns:
            axs[0].plot(df_with_indicators.index, df_with_indicators['bb_high'], label='BB Upper', color='red', linestyle='--', alpha=0.5)
            axs[0].plot(df_with_indicators.index, df_with_indicators['bb_mid'], label='BB Middle', color='orange', linestyle='--', alpha=0.5)
            axs[0].plot(df_with_indicators.index, df_with_indicators['bb_low'], label='BB Lower', color='green', linestyle='--', alpha=0.5)

        axs[0].set_title(f'Advanced Chart - {file_name}')
        axs[0].set_ylabel('Price')
        axs[0].grid(True, alpha=0.3)
        axs[0].legend(loc='upper left')

        # Plot 2: RSI
        if 'rsi' in df_with_indicators.columns:
            axs[1].plot(df_with_indicators.index, df_with_indicators['rsi'], label='RSI', color='purple')
            axs[1].axhline(y=70, color='red', linestyle='--', alpha=0.5)
            axs[1].axhline(y=30, color='green', linestyle='--', alpha=0.5)
            axs[1].set_ylabel('RSI')
            axs[1].set_ylim(0, 100)
            axs[1].grid(True, alpha=0.3)
            axs[1].legend(loc='upper left')

        # Plot 3: MACD
        if 'macd' in df_with_indicators.columns and 'macd_signal' in df_with_indicators.columns:
            axs[2].plot(df_with_indicators.index, df_with_indicators['macd'], label='MACD', color='blue')
            axs[2].plot(df_with_indicators.index, df_with_indicators['macd_signal'], label='Signal', color='red')
            axs[2].set_ylabel('MACD')
            axs[2].grid(True, alpha=0.3)
            axs[2].legend(loc='upper left')

        # Format x-axis dates
        plt.gcf().autofmt_xdate()
        axs[2].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        axs[2].set_xlabel('Date')

        # Save chart
        output_file = os.path.join(charts_dir, f"{os.path.splitext(file_name)[0]}_advanced_chart.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"Saved advanced chart to {output_file}")

        # Close figure to free memory
        plt.close()

    except Exception as e:
        print(f"Error generating advanced chart for {file_name}: {e}")


if __name__ == "__main__":
    main()
