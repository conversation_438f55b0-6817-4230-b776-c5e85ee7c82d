"""
Reinforcement Learning environment for trading
"""

import gym
import numpy as np
import pandas as pd
from gym import spaces
from typing import Optional, List, Dict, Union, Tuple, Any


class TradingEnv(gym.Env):
    """
    Custom trading environment for RL agents
    """

    metadata = {'render.modes': ['human']}

    def __init__(self, df: pd.DataFrame,
                window_size: int = 20,
                initial_balance: float = 1000.0,
                transaction_fee: float = 0.001,
                reward_scaling: float = 1.0,
                min_trade_timeout: int = 0,
                features: List[str] = None):
        """
        Initialize the trading environment

        Args:
            df: DataFrame with OHLCV data and technical indicators
            window_size: Number of past observations to include in the state
            initial_balance: Initial balance in USD
            transaction_fee: Transaction fee as a percentage (0.001 = 0.1%)
            reward_scaling: Scaling factor for rewards
            min_trade_timeout: Minimum number of steps between trades (0 = no timeout)
            features: List of features to include in the state. If None, use all features
        """
        super(TradingEnv, self).__init__()

        self.df = df.copy()
        self.window_size = window_size
        self.initial_balance = initial_balance
        self.transaction_fee = transaction_fee
        self.reward_scaling = reward_scaling
        self.min_trade_timeout = min_trade_timeout

        # Select features to use
        if features is None:
            # Exclude datetime, open, high, low, close, volume from features
            # as they will be normalized separately
            self.features = [col for col in df.columns
                            if col not in ['datetime', 'o', 'h', 'l', 'c', 'v', 'unixTime', 'address', 'type']]
        else:
            self.features = features

        # Add OHLCV columns to features for normalization
        self.ohlcv_cols = ['o', 'h', 'l', 'c', 'v']
        self.all_features = self.ohlcv_cols + self.features

        # Calculate the size of the observation space
        self.obs_shape = (len(self.all_features) * window_size + 5,)  # +5 for balance, holdings, cost_basis, short_term_change, medium_term_change

        # Define action and observation space
        # Actions: 0 = hold, 1 = buy, 2 = sell
        self.action_space = spaces.Discrete(3)

        # Observation space: normalized OHLCV data and technical indicators
        self.observation_space = spaces.Box(
            low=-np.inf, high=np.inf, shape=self.obs_shape, dtype=np.float32
        )

        # Initialize state variables
        self.reset()

    def reset(self):
        """
        Reset the environment to the initial state

        Returns:
            Initial observation
        """
        # Reset position in the data
        self.current_step = self.window_size

        # Reset account state
        self.balance = self.initial_balance
        self.holdings = 0.0
        self.cost_basis = 0.0
        self.total_reward = 0.0
        self.total_profit = 0.0

        # Reset trade history
        self.trades = []

        # Reset trade timeout tracking
        self.last_trade_step = -self.min_trade_timeout  # Allow immediate trading at start
        self.can_trade = True

        # Get initial observation
        return self._get_observation()

    def step(self, action):
        """
        Take a step in the environment

        Args:
            action: Action to take (0 = hold, 1 = buy, 2 = sell)

        Returns:
            Tuple of (observation, reward, done, info)
        """
        # Get current price
        current_price = self.df.iloc[self.current_step]['c']

        # Initialize reward and info
        reward = 0.0
        info = {}

        # Calculate price change from previous step
        if self.current_step > self.window_size:
            prev_price = self.df.iloc[self.current_step-1]['c']
            price_change_pct = (current_price - prev_price) / prev_price
        else:
            price_change_pct = 0.0

        # Check if we can trade (timeout has passed)
        steps_since_last_trade = self.current_step - self.last_trade_step
        can_trade = steps_since_last_trade >= self.min_trade_timeout

        # Execute action
        if action == 1:  # Buy
            if self.balance > 0 and can_trade:
                # Calculate max amount to buy
                max_buy_amount = self.balance / (current_price * (1 + self.transaction_fee))

                # Update holdings and balance
                self.holdings = max_buy_amount
                self.cost_basis = current_price
                self.balance = 0.0

                # Record trade
                trade_info = {
                    'step': self.current_step,
                    'price': current_price,
                    'type': 'buy',
                    'amount': max_buy_amount,
                    'balance': self.balance,
                    'holdings_value': self.holdings * current_price
                }

                # Add datetime if available in the DataFrame
                if self.df.index.name == 'datetime' or isinstance(self.df.index, pd.DatetimeIndex):
                    trade_info['datetime'] = self.df.index[self.current_step]

                self.trades.append(trade_info)

                # Update last trade step
                self.last_trade_step = self.current_step

                # More significant negative reward for buying to discourage immediate buying
                # Include both transaction fee and a fixed penalty
                transaction_cost = self.transaction_fee * self.holdings * current_price
                buy_penalty = 0.01 * self.initial_balance  # Small fixed penalty for buying
                reward = -(transaction_cost + buy_penalty) * self.reward_scaling
            elif self.balance > 0 and not can_trade:
                # Cannot trade due to timeout, small negative reward to discourage trying
                reward = -0.1 * self.reward_scaling

        elif action == 2:  # Sell
            if self.holdings > 0 and can_trade:
                # Calculate sale value
                sale_value = self.holdings * current_price * (1 - self.transaction_fee)

                # Calculate profit
                profit = sale_value - (self.holdings * self.cost_basis)

                # Update balance and holdings
                self.balance = sale_value
                self.total_profit += profit
                self.holdings = 0.0
                self.cost_basis = 0.0

                # Record trade
                trade_info = {
                    'step': self.current_step,
                    'price': current_price,
                    'type': 'sell',
                    'amount': self.holdings,
                    'balance': self.balance,
                    'holdings_value': 0.0,
                    'profit': profit
                }

                # Add datetime if available in the DataFrame
                if self.df.index.name == 'datetime' or isinstance(self.df.index, pd.DatetimeIndex):
                    trade_info['datetime'] = self.df.index[self.current_step]

                self.trades.append(trade_info)

                # Update last trade step
                self.last_trade_step = self.current_step

                # Reward is proportional to profit
                reward = profit * self.reward_scaling
            elif self.holdings > 0 and not can_trade:
                # Cannot trade due to timeout, small negative reward to discourage trying
                reward = -0.1 * self.reward_scaling

        # Reward for holding cash during price drops
        if action == 0 and self.balance > 0 and price_change_pct < 0:
            # Reward for avoiding loss by holding cash during price drop
            # The reward is proportional to the price drop and the balance
            cash_holding_reward = abs(price_change_pct) * self.balance * 0.1 * self.reward_scaling
            reward += cash_holding_reward

        # Penalty for holding coins during price drops
        if action == 0 and self.holdings > 0 and price_change_pct < 0:
            # Small penalty for holding during price drop
            holding_penalty = abs(price_change_pct) * self.holdings * current_price * 0.05 * self.reward_scaling
            reward -= holding_penalty

        # Move to next step
        self.current_step += 1

        # Calculate portfolio value
        portfolio_value = self.balance + (self.holdings * current_price)

        # Check if episode is done
        done = self.current_step >= len(self.df) - 1

        # Update total reward
        self.total_reward += reward

        # Calculate trade timeout information
        steps_since_last_trade = self.current_step - self.last_trade_step
        can_trade = steps_since_last_trade >= self.min_trade_timeout
        steps_until_can_trade = max(0, self.min_trade_timeout - steps_since_last_trade)

        # Additional info
        info = {
            'step': self.current_step,
            'balance': self.balance,
            'holdings': self.holdings,
            'holdings_value': self.holdings * current_price,
            'portfolio_value': portfolio_value,
            'price': current_price,
            'price_change_pct': price_change_pct,
            'total_reward': self.total_reward,
            'total_profit': self.total_profit,
            'can_trade': can_trade,
            'steps_until_can_trade': steps_until_can_trade
        }

        # Add datetime if available in the DataFrame
        if self.df.index.name == 'datetime' or isinstance(self.df.index, pd.DatetimeIndex):
            info['datetime'] = self.df.index[self.current_step]

        # Get new observation
        obs = self._get_observation()

        return obs, reward, done, info

    def _get_observation(self):
        """
        Get the current observation

        Returns:
            Numpy array representing the current state
        """
        # Get window of data
        start = self.current_step - self.window_size + 1
        end = self.current_step + 1
        window = self.df.iloc[start:end].copy()

        # Normalize the window data
        window_normalized = self._normalize_window(window)

        # Flatten the window
        obs_market = window_normalized.values.flatten()

        # Add account state and price trend information
        current_price = self.df.iloc[self.current_step]['c']

        # Calculate price trend features
        if self.current_step > self.window_size:
            # Calculate short-term price change (last 5 steps)
            short_lookback = min(5, self.current_step - self.window_size)
            short_term_change = (current_price - self.df.iloc[self.current_step - short_lookback]['c']) / self.df.iloc[self.current_step - short_lookback]['c']

            # Calculate medium-term price change (last 20 steps)
            medium_lookback = min(20, self.current_step - self.window_size)
            medium_term_change = (current_price - self.df.iloc[self.current_step - medium_lookback]['c']) / self.df.iloc[self.current_step - medium_lookback]['c']
        else:
            short_term_change = 0.0
            medium_term_change = 0.0

        obs_account = np.array([
            self.balance / self.initial_balance,  # Normalized balance
            self.holdings * current_price / self.initial_balance,  # Normalized holdings value
            self.cost_basis / current_price if self.cost_basis > 0 else 0,  # Normalized cost basis
            short_term_change,  # Short-term price trend
            medium_term_change  # Medium-term price trend
        ])

        # Combine market and account observations
        obs = np.concatenate([obs_market, obs_account])

        return obs.astype(np.float32)

    def _normalize_window(self, window: pd.DataFrame) -> pd.DataFrame:
        """
        Normalize the window data

        Args:
            window: DataFrame with window data

        Returns:
            Normalized DataFrame
        """
        result = window[self.all_features].copy()

        # Normalize OHLCV data relative to the last close price
        last_close = result.iloc[-1]['c']
        for col in self.ohlcv_cols:
            if col in result.columns:
                if col == 'v':  # Volume is normalized differently
                    result[col] = result[col] / result[col].mean()
                else:  # Price columns are normalized by the last close
                    result[col] = result[col] / last_close - 1.0

        # Z-score normalization for other features
        for col in self.features:
            if col in result.columns:
                mean = result[col].mean()
                std = result[col].std()
                if std > 0:
                    result[col] = (result[col] - mean) / std
                else:
                    result[col] = 0.0

        return result

    def render(self, mode='human'):
        """
        Render the environment

        Args:
            mode: Rendering mode
        """
        if mode != 'human':
            raise NotImplementedError(f"Render mode {mode} is not implemented")

        current_price = self.df.iloc[self.current_step]['c']
        portfolio_value = self.balance + (self.holdings * current_price)

        # Calculate trade timeout information
        steps_since_last_trade = self.current_step - self.last_trade_step
        can_trade = steps_since_last_trade >= self.min_trade_timeout
        steps_until_can_trade = max(0, self.min_trade_timeout - steps_since_last_trade)

        print(f"Step: {self.current_step}")
        print(f"Price: {current_price:.6f}")
        print(f"Balance: {self.balance:.2f}")
        print(f"Holdings: {self.holdings:.6f} (${self.holdings * current_price:.2f})")
        print(f"Portfolio Value: ${portfolio_value:.2f}")
        print(f"Total Profit: ${self.total_profit:.2f}")
        print(f"Total Reward: {self.total_reward:.2f}")

        # Display trade timeout information
        if self.min_trade_timeout > 0:
            print(f"Can Trade: {can_trade}")
            if not can_trade:
                print(f"Steps until can trade: {steps_until_can_trade}")

        print("-" * 50)

    def close(self):
        """
        Close the environment
        """
        pass
