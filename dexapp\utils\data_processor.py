"""
Data processing utilities for loading and preprocessing trading data
"""

import os
import glob
import pandas as pd
import numpy as np
from typing import Optional, List, Dict, Union, Tuple
import matplotlib.pyplot as plt
import seaborn as sns


class DataProcessor:
    """
    Class for loading and preprocessing trading data
    """

    def __init__(self, data_dir: str = "input"):
        """
        Initialize the data processor

        Args:
            data_dir: Directory containing the data files
        """
        self.data_dir = data_dir
        self.data = None

    def load_data(self, file_path: Optional[str] = None) -> pd.DataFrame:
        """
        Load data from a CSV file or all CSV files in the data directory

        Args:
            file_path: Path to the CSV file. If None, load all CSV files in data_dir

        Returns:
            DataFrame containing the loaded data
        """
        if file_path is not None:
            # Load a single file
            full_path = os.path.join(self.data_dir, file_path) if not os.path.isabs(file_path) else file_path
            df = self._load_single_file(full_path)
        else:
            # Load all CSV files in the directory
            csv_files = glob.glob(os.path.join(self.data_dir, "*.csv"))

            if not csv_files:
                raise ValueError(f"No CSV files found in {self.data_dir}")

            # Load and concatenate all CSV files
            dfs = []
            for file_path in csv_files:
                try:
                    df = self._load_single_file(file_path)
                    dfs.append(df)
                    print(f"Loaded {file_path} with {len(df)} rows")
                except Exception as e:
                    print(f"Error loading {file_path}: {e}")

            if not dfs:
                raise ValueError("No valid CSV files could be loaded")

            # Concatenate all dataframes
            df = pd.concat(dfs)

            # Sort by datetime index
            df.sort_index(inplace=True)

            print(f"Combined {len(dfs)} files into a dataset with {len(df)} rows")

        self.data = df
        return df

    def _load_single_file(self, file_path: str) -> pd.DataFrame:
        """
        Load a single CSV file

        Args:
            file_path: Path to the CSV file

        Returns:
            DataFrame containing the loaded data
        """
        df = pd.read_csv(file_path)

        # Convert unixTime to datetime
        if 'unixTime' in df.columns:
            df['datetime'] = pd.to_datetime(df['unixTime'], unit='s')
            df.set_index('datetime', inplace=True)

        return df

    def preprocess_data(self, df: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        Preprocess the data for analysis and training

        Args:
            df: DataFrame to preprocess. If None, use self.data

        Returns:
            Preprocessed DataFrame
        """
        if df is None:
            df = self.data.copy()

        if df is None:
            raise ValueError("No data loaded. Call load_data first or provide a DataFrame.")

        # Ensure OHLCV columns are present
        required_cols = ['o', 'h', 'l', 'c', 'v']
        if not all(col in df.columns for col in required_cols):
            raise ValueError(f"Data must contain OHLCV columns: {required_cols}")

        # Convert columns to numeric if needed
        for col in required_cols:
            df[col] = pd.to_numeric(df[col])

        # Calculate percentage changes
        df['pct_change'] = df['c'].pct_change()

        # Calculate relative volume (compared to 10-period moving average)
        df['rel_volume'] = df['v'] / df['v'].rolling(10).mean()

        # Drop rows with NaN values
        df.dropna(inplace=True)

        return df

    def split_data(self, df: Optional[pd.DataFrame] = None,
                  train_ratio: float = 0.7,
                  val_ratio: float = 0.15) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        Split data into training, validation, and test sets

        Args:
            df: DataFrame to split. If None, use self.data
            train_ratio: Ratio of data to use for training
            val_ratio: Ratio of data to use for validation

        Returns:
            Tuple of (train_df, val_df, test_df)
        """
        if df is None:
            df = self.data.copy()

        if df is None:
            raise ValueError("No data loaded. Call load_data first or provide a DataFrame.")

        n = len(df)
        train_end = int(n * train_ratio)
        val_end = train_end + int(n * val_ratio)

        train_df = df.iloc[:train_end]
        val_df = df.iloc[train_end:val_end]
        test_df = df.iloc[val_end:]

        return train_df, val_df, test_df

    def visualize_data(self, df: Optional[pd.DataFrame] = None,
                      columns: List[str] = None,
                      start_date: Optional[str] = None,
                      end_date: Optional[str] = None,
                      figsize: Tuple[int, int] = (12, 8)) -> None:
        """
        Visualize the data

        Args:
            df: DataFrame to visualize. If None, use self.data
            columns: List of columns to visualize. If None, use OHLCV columns
            start_date: Start date for visualization
            end_date: End date for visualization
            figsize: Figure size
        """
        if df is None:
            df = self.data.copy()

        if df is None:
            raise ValueError("No data loaded. Call load_data first or provide a DataFrame.")

        if columns is None:
            columns = ['o', 'h', 'l', 'c', 'v']

        # Filter by date if specified
        if start_date is not None or end_date is not None:
            df = df.loc[start_date:end_date]

        # Set up the figure
        fig, axes = plt.subplots(len(columns), 1, figsize=figsize, sharex=True)
        if len(columns) == 1:
            axes = [axes]

        # Plot each column
        for i, col in enumerate(columns):
            if col in df.columns:
                axes[i].plot(df.index, df[col])
                axes[i].set_title(col)
                axes[i].grid(True)

        plt.tight_layout()
        plt.show()
