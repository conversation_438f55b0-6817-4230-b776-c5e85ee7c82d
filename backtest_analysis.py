"""
Detailed backtest analysis script for the DexApp trading bot

This script loads a trained model, performs a backtest on a specific CSV file,
and displays a detailed analysis with charts showing when trades were made,
buy/sell prices, and performance metrics.

Usage:
    python backtest_analysis.py --model_file dexapp/models/ppo_model.zip --data_file input/your_file.csv
"""

import os
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Patch
from typing import Optional, List, Dict, Union, Tuple, Any

from dexapp.utils.data_processor import DataProcessor
from dexapp.indicators.technical import TechnicalIndicators
from dexapp.environment.trading_env import TradingEnv
from dexapp.agents.rl_agent import PPOAgent, DQNAgent, A2CAgent


def parse_args():
    """
    Parse command line arguments
    
    Returns:
        Parsed arguments
    """
    parser = argparse.ArgumentParser(description='DexApp Trading Bot - Detailed Backtest Analysis')
    
    # Model arguments
    parser.add_argument('--model_file', type=str, required=True,
                       help='Path to the trained model file (e.g., dexapp/models/ppo_model.zip)')
    parser.add_argument('--agent', type=str, choices=['ppo', 'dqn', 'a2c'], default='ppo',
                       help='RL agent type used for the model')
    
    # Data arguments
    parser.add_argument('--data_dir', type=str, default='input',
                       help='Directory containing data files')
    parser.add_argument('--data_file', type=str, required=True,
                       help='Specific data file to use for backtesting')
    
    # Environment arguments
    parser.add_argument('--window_size', type=int, default=20,
                       help='Window size for observations')
    parser.add_argument('--initial_balance', type=float, default=1000.0,
                       help='Initial balance for trading')
    parser.add_argument('--transaction_fee', type=float, default=0.001,
                       help='Transaction fee as a percentage')
    
    # Output arguments
    parser.add_argument('--output_dir', type=str, default='dexapp/results',
                       help='Directory to save results')
    parser.add_argument('--save_plots', action='store_true',
                       help='Save plots to output directory')
    
    return parser.parse_args()


def run_backtest(args):
    """
    Run a detailed backtest analysis
    
    Args:
        args: Command line arguments
    """
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load and preprocess data
    data_processor = DataProcessor(data_dir=args.data_dir)
    df = data_processor.load_data(args.data_file)
    
    print(f"Loaded data with shape: {df.shape}")
    print(df.head())
    
    # Add technical indicators
    df = TechnicalIndicators.add_all_indicators(df)
    
    # Drop rows with NaN values
    df.dropna(inplace=True)
    
    print(f"Processed data with shape: {df.shape}")
    
    # Create environment
    env = TradingEnv(
        df=df,
        window_size=args.window_size,
        initial_balance=args.initial_balance,
        transaction_fee=args.transaction_fee
    )
    
    # Create agent based on type
    if args.agent == 'ppo':
        agent = PPOAgent(env)
    elif args.agent == 'dqn':
        agent = DQNAgent(env)
    elif args.agent == 'a2c':
        agent = A2CAgent(env)
    else:
        raise ValueError(f"Unknown agent type: {args.agent}")
    
    # Load the trained model
    agent.load(args.model_file)
    
    # Run backtest
    results_df = agent.backtest(env)
    
    # Save results to CSV
    results_file = os.path.join(args.output_dir, f"backtest_results_{os.path.basename(args.data_file)}.csv")
    results_df.to_csv(results_file, index=False)
    print(f"Saved backtest results to {results_file}")
    
    # Extract trades from the environment
    trades = env.trades
    
    # Create a DataFrame for trades
    if trades:
        trades_df = pd.DataFrame(trades)
        trades_file = os.path.join(args.output_dir, f"trades_{os.path.basename(args.data_file)}.csv")
        trades_df.to_csv(trades_file, index=False)
        print(f"Saved trades to {trades_file}")
    else:
        trades_df = pd.DataFrame()
        print("No trades were executed during the backtest")
    
    # Analyze and visualize the results
    analyze_results(df, results_df, trades_df, args)


def analyze_results(data_df, results_df, trades_df, args):
    """
    Analyze and visualize backtest results
    
    Args:
        data_df: Original data DataFrame with indicators
        results_df: Results DataFrame from backtest
        trades_df: DataFrame containing trade information
        args: Command line arguments
    """
    # Print summary statistics
    print("\n" + "="*50)
    print("BACKTEST SUMMARY")
    print("="*50)
    
    # Calculate performance metrics
    initial_balance = args.initial_balance
    final_balance = results_df.iloc[-1]['portfolio_value'] if not results_df.empty else initial_balance
    
    total_return = (final_balance - initial_balance) / initial_balance
    total_return_pct = total_return * 100
    
    # Calculate daily returns if we have portfolio values
    if 'portfolio_value' in results_df.columns and len(results_df) > 1:
        portfolio_values = results_df['portfolio_value']
        returns = portfolio_values.pct_change().dropna()
        
        # Calculate annualized metrics (assuming 252 trading days per year)
        annual_return = total_return * (252 / len(results_df)) * 100
        volatility = returns.std() * np.sqrt(252) * 100
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0
        
        # Calculate drawdown
        cumulative_returns = (1 + returns).cumprod()
        running_max = cumulative_returns.cummax()
        drawdown = (cumulative_returns / running_max - 1) * 100
        max_drawdown = drawdown.min()
        
        # Calculate win rate if we have trades
        if not trades_df.empty and 'profit' in trades_df.columns:
            winning_trades = trades_df[trades_df['profit'] > 0]
            win_rate = len(winning_trades) / len(trades_df) * 100 if len(trades_df) > 0 else 0
            
            # Calculate average profit/loss
            avg_profit = winning_trades['profit'].mean() if len(winning_trades) > 0 else 0
            
            losing_trades = trades_df[trades_df['profit'] <= 0]
            avg_loss = losing_trades['profit'].mean() if len(losing_trades) > 0 else 0
            
            # Calculate profit factor
            total_profit = winning_trades['profit'].sum() if len(winning_trades) > 0 else 0
            total_loss = abs(losing_trades['profit'].sum()) if len(losing_trades) > 0 else 0
            profit_factor = total_profit / total_loss if total_loss > 0 else float('inf')
        else:
            win_rate = 0
            avg_profit = 0
            avg_loss = 0
            profit_factor = 0
    else:
        annual_return = 0
        volatility = 0
        sharpe_ratio = 0
        max_drawdown = 0
        win_rate = 0
        avg_profit = 0
        avg_loss = 0
        profit_factor = 0
    
    # Print performance metrics
    print(f"Initial Balance: ${initial_balance:.2f}")
    print(f"Final Balance: ${final_balance:.2f}")
    print(f"Total Return: {total_return_pct:.2f}%")
    print(f"Annualized Return: {annual_return:.2f}%")
    print(f"Volatility: {volatility:.2f}%")
    print(f"Sharpe Ratio: {sharpe_ratio:.2f}")
    print(f"Maximum Drawdown: {max_drawdown:.2f}%")
    
    if not trades_df.empty:
        print(f"\nTotal Trades: {len(trades_df)}")
        print(f"Win Rate: {win_rate:.2f}%")
        print(f"Average Profit: ${avg_profit:.2f}")
        print(f"Average Loss: ${avg_loss:.2f}")
        print(f"Profit Factor: {profit_factor:.2f}")
    
    # Create visualizations
    create_visualizations(data_df, results_df, trades_df, args)


def create_visualizations(data_df, results_df, trades_df, args):
    """
    Create visualizations for backtest results
    
    Args:
        data_df: Original data DataFrame with indicators
        results_df: Results DataFrame from backtest
        trades_df: DataFrame containing trade information
        args: Command line arguments
    """
    # Set up the figure
    plt.figure(figsize=(15, 12))
    
    # 1. Price chart with buy/sell signals
    ax1 = plt.subplot(3, 1, 1)
    
    # Plot price
    if 'datetime' in results_df.columns:
        x = pd.to_datetime(results_df['datetime'])
    else:
        x = results_df['step']
    
    # Plot price
    ax1.plot(x, results_df['price'], label='Price', color='blue', alpha=0.7)
    
    # Plot buy/sell signals if we have trades
    if not trades_df.empty:
        # Get buy trades
        buy_trades = trades_df[trades_df['type'] == 'buy']
        if not buy_trades.empty:
            if 'datetime' in buy_trades.columns:
                buy_x = pd.to_datetime(buy_trades['datetime'])
            else:
                buy_x = [results_df.iloc[step]['step'] for step in buy_trades['step']]
            
            buy_y = buy_trades['price']
            ax1.scatter(buy_x, buy_y, color='green', marker='^', s=100, label='Buy')
        
        # Get sell trades
        sell_trades = trades_df[trades_df['type'] == 'sell']
        if not sell_trades.empty:
            if 'datetime' in sell_trades.columns:
                sell_x = pd.to_datetime(sell_trades['datetime'])
            else:
                sell_x = [results_df.iloc[step]['step'] for step in sell_trades['step']]
            
            sell_y = sell_trades['price']
            ax1.scatter(sell_x, sell_y, color='red', marker='v', s=100, label='Sell')
    
    ax1.set_title('Price Chart with Buy/Sell Signals')
    ax1.set_ylabel('Price')
    ax1.grid(True)
    ax1.legend()
    
    # 2. Portfolio value
    ax2 = plt.subplot(3, 1, 2, sharex=ax1)
    ax2.plot(x, results_df['portfolio_value'], label='Portfolio Value', color='purple')
    ax2.set_title('Portfolio Value')
    ax2.set_ylabel('Value ($)')
    ax2.grid(True)
    
    # 3. Holdings value
    ax3 = plt.subplot(3, 1, 3, sharex=ax1)
    ax3.plot(x, results_df['holdings_value'], label='Holdings Value', color='orange')
    ax3.set_title('Holdings Value')
    ax3.set_ylabel('Value ($)')
    ax3.grid(True)
    
    # Format x-axis
    if 'datetime' in results_df.columns:
        ax3.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d %H:%M'))
        plt.xticks(rotation=45)
    
    plt.tight_layout()
    
    # Save or show the plot
    if args.save_plots:
        plot_file = os.path.join(args.output_dir, f"backtest_plot_{os.path.basename(args.data_file).split('.')[0]}.png")
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        print(f"Saved plot to {plot_file}")
    
    plt.show()
    
    # Create additional plots if we have trades
    if not trades_df.empty:
        create_trade_analysis_plots(data_df, results_df, trades_df, args)


def create_trade_analysis_plots(data_df, results_df, trades_df, args):
    """
    Create additional plots for trade analysis
    
    Args:
        data_df: Original data DataFrame with indicators
        results_df: Results DataFrame from backtest
        trades_df: DataFrame containing trade information
        args: Command line arguments
    """
    # Only proceed if we have trades
    if trades_df.empty or 'profit' not in trades_df.columns:
        return
    
    # 1. Trade profit distribution
    plt.figure(figsize=(15, 10))
    
    # Trade profit histogram
    ax1 = plt.subplot(2, 2, 1)
    trades_df['profit'].hist(bins=20, ax=ax1, color='skyblue', edgecolor='black')
    ax1.axvline(0, color='red', linestyle='--')
    ax1.set_title('Trade Profit Distribution')
    ax1.set_xlabel('Profit ($)')
    ax1.set_ylabel('Frequency')
    
    # Cumulative profit
    ax2 = plt.subplot(2, 2, 2)
    if 'profit' in trades_df.columns:
        cumulative_profit = trades_df['profit'].cumsum()
        ax2.plot(cumulative_profit.index, cumulative_profit, color='green')
        ax2.set_title('Cumulative Profit')
        ax2.set_xlabel('Trade Number')
        ax2.set_ylabel('Cumulative Profit ($)')
        ax2.grid(True)
    
    # Trade duration
    ax3 = plt.subplot(2, 2, 3)
    if 'type' in trades_df.columns and len(trades_df[trades_df['type'] == 'sell']) > 0:
        # Calculate trade durations
        buy_trades = trades_df[trades_df['type'] == 'buy'].reset_index(drop=True)
        sell_trades = trades_df[trades_df['type'] == 'sell'].reset_index(drop=True)
        
        # Make sure we have matching buy/sell pairs
        min_len = min(len(buy_trades), len(sell_trades))
        
        if min_len > 0:
            durations = sell_trades['step'].iloc[:min_len].values - buy_trades['step'].iloc[:min_len].values
            plt.hist(durations, bins=20, color='lightgreen', edgecolor='black')
            ax3.set_title('Trade Duration Distribution')
            ax3.set_xlabel('Duration (steps)')
            ax3.set_ylabel('Frequency')
    
    # Profit vs. trade duration scatter plot
    ax4 = plt.subplot(2, 2, 4)
    if 'type' in trades_df.columns and 'profit' in trades_df.columns and len(trades_df[trades_df['type'] == 'sell']) > 0:
        # Calculate trade durations and profits
        buy_trades = trades_df[trades_df['type'] == 'buy'].reset_index(drop=True)
        sell_trades = trades_df[trades_df['type'] == 'sell'].reset_index(drop=True)
        
        # Make sure we have matching buy/sell pairs
        min_len = min(len(buy_trades), len(sell_trades))
        
        if min_len > 0:
            durations = sell_trades['step'].iloc[:min_len].values - buy_trades['step'].iloc[:min_len].values
            profits = sell_trades['profit'].iloc[:min_len].values
            
            # Create scatter plot
            scatter = ax4.scatter(durations, profits, c=profits, cmap='coolwarm', alpha=0.7)
            plt.colorbar(scatter, ax=ax4, label='Profit ($)')
            ax4.set_title('Profit vs. Trade Duration')
            ax4.set_xlabel('Duration (steps)')
            ax4.set_ylabel('Profit ($)')
            ax4.grid(True)
            ax4.axhline(0, color='black', linestyle='--')
    
    plt.tight_layout()
    
    # Save or show the plot
    if args.save_plots:
        plot_file = os.path.join(args.output_dir, f"trade_analysis_{os.path.basename(args.data_file).split('.')[0]}.png")
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        print(f"Saved trade analysis plot to {plot_file}")
    
    plt.show()
    
    # Create technical indicator analysis if we have the data
    if not data_df.empty and 'rsi' in data_df.columns:
        create_indicator_analysis_plots(data_df, results_df, trades_df, args)


def create_indicator_analysis_plots(data_df, results_df, trades_df, args):
    """
    Create plots analyzing trading decisions in relation to technical indicators
    
    Args:
        data_df: Original data DataFrame with indicators
        results_df: Results DataFrame from backtest
        trades_df: DataFrame containing trade information
        args: Command line arguments
    """
    # Set up the figure
    plt.figure(figsize=(15, 15))
    
    # 1. RSI with buy/sell signals
    ax1 = plt.subplot(3, 1, 1)
    
    # Plot RSI
    if 'step' in results_df.columns and 'rsi' in data_df.columns:
        # Map steps to data_df indices
        step_to_idx = {step: i for i, step in enumerate(range(len(data_df)))}
        
        # Get RSI values for each step
        rsi_values = [data_df['rsi'].iloc[step_to_idx.get(step, 0)] if step in step_to_idx else None 
                     for step in results_df['step']]
        
        # Plot RSI
        ax1.plot(results_df['step'], rsi_values, label='RSI', color='blue')
        
        # Add overbought/oversold lines
        ax1.axhline(70, color='red', linestyle='--', alpha=0.5, label='Overbought')
        ax1.axhline(30, color='green', linestyle='--', alpha=0.5, label='Oversold')
        
        # Plot buy/sell signals if we have trades
        if not trades_df.empty:
            # Get buy trades
            buy_trades = trades_df[trades_df['type'] == 'buy']
            if not buy_trades.empty:
                buy_steps = buy_trades['step'].values
                buy_rsi = [data_df['rsi'].iloc[step_to_idx.get(step, 0)] if step in step_to_idx else None 
                          for step in buy_steps]
                ax1.scatter(buy_steps, buy_rsi, color='green', marker='^', s=100, label='Buy')
            
            # Get sell trades
            sell_trades = trades_df[trades_df['type'] == 'sell']
            if not sell_trades.empty:
                sell_steps = sell_trades['step'].values
                sell_rsi = [data_df['rsi'].iloc[step_to_idx.get(step, 0)] if step in step_to_idx else None 
                           for step in sell_steps]
                ax1.scatter(sell_steps, sell_rsi, color='red', marker='v', s=100, label='Sell')
    
    ax1.set_title('RSI with Buy/Sell Signals')
    ax1.set_ylabel('RSI')
    ax1.set_ylim(0, 100)
    ax1.grid(True)
    ax1.legend()
    
    # 2. MACD with buy/sell signals
    ax2 = plt.subplot(3, 1, 2, sharex=ax1)
    
    # Plot MACD
    if 'step' in results_df.columns and 'macd' in data_df.columns and 'macd_signal' in data_df.columns:
        # Get MACD values for each step
        macd_values = [data_df['macd'].iloc[step_to_idx.get(step, 0)] if step in step_to_idx else None 
                      for step in results_df['step']]
        
        macd_signal_values = [data_df['macd_signal'].iloc[step_to_idx.get(step, 0)] if step in step_to_idx else None 
                             for step in results_df['step']]
        
        # Plot MACD and signal line
        ax2.plot(results_df['step'], macd_values, label='MACD', color='blue')
        ax2.plot(results_df['step'], macd_signal_values, label='Signal', color='red')
        
        # Plot buy/sell signals if we have trades
        if not trades_df.empty:
            # Get buy trades
            buy_trades = trades_df[trades_df['type'] == 'buy']
            if not buy_trades.empty:
                buy_steps = buy_trades['step'].values
                buy_macd = [data_df['macd'].iloc[step_to_idx.get(step, 0)] if step in step_to_idx else None 
                           for step in buy_steps]
                ax2.scatter(buy_steps, buy_macd, color='green', marker='^', s=100, label='Buy')
            
            # Get sell trades
            sell_trades = trades_df[trades_df['type'] == 'sell']
            if not sell_trades.empty:
                sell_steps = sell_trades['step'].values
                sell_macd = [data_df['macd'].iloc[step_to_idx.get(step, 0)] if step in step_to_idx else None 
                            for step in sell_steps]
                ax2.scatter(sell_steps, sell_macd, color='red', marker='v', s=100, label='Sell')
    
    ax2.set_title('MACD with Buy/Sell Signals')
    ax2.set_ylabel('MACD')
    ax2.grid(True)
    ax2.legend()
    
    # 3. Bollinger Bands with buy/sell signals
    ax3 = plt.subplot(3, 1, 3, sharex=ax1)
    
    # Plot price and Bollinger Bands
    if ('step' in results_df.columns and 'price' in results_df.columns and 
        'bb_high' in data_df.columns and 'bb_mid' in data_df.columns and 'bb_low' in data_df.columns):
        
        # Get Bollinger Band values for each step
        bb_high_values = [data_df['bb_high'].iloc[step_to_idx.get(step, 0)] if step in step_to_idx else None 
                         for step in results_df['step']]
        
        bb_mid_values = [data_df['bb_mid'].iloc[step_to_idx.get(step, 0)] if step in step_to_idx else None 
                        for step in results_df['step']]
        
        bb_low_values = [data_df['bb_low'].iloc[step_to_idx.get(step, 0)] if step in step_to_idx else None 
                        for step in results_df['step']]
        
        # Plot price and Bollinger Bands
        ax3.plot(results_df['step'], results_df['price'], label='Price', color='blue')
        ax3.plot(results_df['step'], bb_high_values, label='Upper Band', color='red', linestyle='--')
        ax3.plot(results_df['step'], bb_mid_values, label='Middle Band', color='green', linestyle='-')
        ax3.plot(results_df['step'], bb_low_values, label='Lower Band', color='red', linestyle='--')
        
        # Plot buy/sell signals if we have trades
        if not trades_df.empty:
            # Get buy trades
            buy_trades = trades_df[trades_df['type'] == 'buy']
            if not buy_trades.empty:
                buy_steps = buy_trades['step'].values
                buy_prices = buy_trades['price'].values
                ax3.scatter(buy_steps, buy_prices, color='green', marker='^', s=100, label='Buy')
            
            # Get sell trades
            sell_trades = trades_df[trades_df['type'] == 'sell']
            if not sell_trades.empty:
                sell_steps = sell_trades['step'].values
                sell_prices = sell_trades['price'].values
                ax3.scatter(sell_steps, sell_prices, color='red', marker='v', s=100, label='Sell')
    
    ax3.set_title('Price with Bollinger Bands and Buy/Sell Signals')
    ax3.set_ylabel('Price')
    ax3.grid(True)
    ax3.legend()
    
    plt.tight_layout()
    
    # Save or show the plot
    if args.save_plots:
        plot_file = os.path.join(args.output_dir, f"indicator_analysis_{os.path.basename(args.data_file).split('.')[0]}.png")
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        print(f"Saved indicator analysis plot to {plot_file}")
    
    plt.show()


def main():
    """
    Main function
    """
    args = parse_args()
    run_backtest(args)


if __name__ == "__main__":
    main()
