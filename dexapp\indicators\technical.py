"""
Technical indicators for trading analysis
"""

import pandas as pd
import numpy as np
from typing import Optional, List, Dict, Union, Tuple
import ta


class TechnicalIndicators:
    """
    Class for calculating technical indicators for trading
    """
    
    @staticmethod
    def add_all_indicators(df: pd.DataFrame, 
                          ohlcv_cols: Dict[str, str] = None) -> pd.DataFrame:
        """
        Add all technical indicators to the DataFrame
        
        Args:
            df: DataFrame with OHLCV data
            ohlcv_cols: Dictionary mapping standard OHLCV column names to actual column names in df
                        Default: {'open': 'o', 'high': 'h', 'low': 'l', 'close': 'c', 'volume': 'v'}
                        
        Returns:
            DataFrame with added technical indicators
        """
        if ohlcv_cols is None:
            ohlcv_cols = {'open': 'o', 'high': 'h', 'low': 'l', 'close': 'c', 'volume': 'v'}
        
        # Create a copy of the DataFrame
        result = df.copy()
        
        # Add RSI
        result = TechnicalIndicators.add_rsi(result, close_col=ohlcv_cols['close'])
        
        # Add MACD
        result = TechnicalIndicators.add_macd(result, close_col=ohlcv_cols['close'])
        
        # Add Bollinger Bands
        result = TechnicalIndicators.add_bollinger_bands(result, close_col=ohlcv_cols['close'])
        
        # Add EMA
        result = TechnicalIndicators.add_ema(result, close_col=ohlcv_cols['close'])
        
        # Add Stochastic Oscillator
        result = TechnicalIndicators.add_stochastic(
            result, 
            high_col=ohlcv_cols['high'], 
            low_col=ohlcv_cols['low'], 
            close_col=ohlcv_cols['close']
        )
        
        # Add additional features
        result = TechnicalIndicators.add_price_features(
            result,
            open_col=ohlcv_cols['open'],
            high_col=ohlcv_cols['high'],
            low_col=ohlcv_cols['low'],
            close_col=ohlcv_cols['close'],
            volume_col=ohlcv_cols['volume']
        )
        
        return result
    
    @staticmethod
    def add_rsi(df: pd.DataFrame, close_col: str = 'c', window: int = 14) -> pd.DataFrame:
        """
        Add Relative Strength Index (RSI) to the DataFrame
        
        Args:
            df: DataFrame with price data
            close_col: Name of the close price column
            window: Window size for RSI calculation
            
        Returns:
            DataFrame with added RSI
        """
        df['rsi'] = ta.momentum.RSIIndicator(
            close=df[close_col], window=window
        ).rsi()
        return df
    
    @staticmethod
    def add_macd(df: pd.DataFrame, close_col: str = 'c', 
                fast_window: int = 12, slow_window: int = 26, 
                signal_window: int = 9) -> pd.DataFrame:
        """
        Add Moving Average Convergence Divergence (MACD) to the DataFrame
        
        Args:
            df: DataFrame with price data
            close_col: Name of the close price column
            fast_window: Window size for fast EMA
            slow_window: Window size for slow EMA
            signal_window: Window size for signal line
            
        Returns:
            DataFrame with added MACD
        """
        macd_indicator = ta.trend.MACD(
            close=df[close_col],
            window_fast=fast_window,
            window_slow=slow_window,
            window_sign=signal_window
        )
        
        df['macd'] = macd_indicator.macd()
        df['macd_signal'] = macd_indicator.macd_signal()
        df['macd_diff'] = macd_indicator.macd_diff()
        
        return df
    
    @staticmethod
    def add_bollinger_bands(df: pd.DataFrame, close_col: str = 'c', 
                           window: int = 20, window_dev: float = 2.0) -> pd.DataFrame:
        """
        Add Bollinger Bands to the DataFrame
        
        Args:
            df: DataFrame with price data
            close_col: Name of the close price column
            window: Window size for moving average
            window_dev: Number of standard deviations
            
        Returns:
            DataFrame with added Bollinger Bands
        """
        bollinger = ta.volatility.BollingerBands(
            close=df[close_col],
            window=window,
            window_dev=window_dev
        )
        
        df['bb_high'] = bollinger.bollinger_hband()
        df['bb_mid'] = bollinger.bollinger_mavg()
        df['bb_low'] = bollinger.bollinger_lband()
        df['bb_width'] = bollinger.bollinger_wband()
        df['bb_pct'] = bollinger.bollinger_pband()
        
        return df
    
    @staticmethod
    def add_ema(df: pd.DataFrame, close_col: str = 'c', 
               windows: List[int] = [5, 10, 20, 50, 200]) -> pd.DataFrame:
        """
        Add Exponential Moving Averages (EMA) to the DataFrame
        
        Args:
            df: DataFrame with price data
            close_col: Name of the close price column
            windows: List of window sizes for EMA calculation
            
        Returns:
            DataFrame with added EMAs
        """
        for window in windows:
            df[f'ema_{window}'] = ta.trend.EMAIndicator(
                close=df[close_col], window=window
            ).ema_indicator()
        
        return df
    
    @staticmethod
    def add_stochastic(df: pd.DataFrame, high_col: str = 'h', 
                      low_col: str = 'l', close_col: str = 'c',
                      window: int = 14, smooth_window: int = 3) -> pd.DataFrame:
        """
        Add Stochastic Oscillator to the DataFrame
        
        Args:
            df: DataFrame with price data
            high_col: Name of the high price column
            low_col: Name of the low price column
            close_col: Name of the close price column
            window: Window size for Stochastic calculation
            smooth_window: Window size for %D line
            
        Returns:
            DataFrame with added Stochastic Oscillator
        """
        stoch = ta.momentum.StochasticOscillator(
            high=df[high_col],
            low=df[low_col],
            close=df[close_col],
            window=window,
            smooth_window=smooth_window
        )
        
        df['stoch_k'] = stoch.stoch()
        df['stoch_d'] = stoch.stoch_signal()
        
        return df
    
    @staticmethod
    def add_price_features(df: pd.DataFrame, open_col: str = 'o', 
                          high_col: str = 'h', low_col: str = 'l', 
                          close_col: str = 'c', volume_col: str = 'v') -> pd.DataFrame:
        """
        Add additional price-based features to the DataFrame
        
        Args:
            df: DataFrame with OHLCV data
            open_col: Name of the open price column
            high_col: Name of the high price column
            low_col: Name of the low price column
            close_col: Name of the close price column
            volume_col: Name of the volume column
            
        Returns:
            DataFrame with added price features
        """
        # Price change percentage
        df['pct_change'] = df[close_col].pct_change()
        
        # Relative volume (compared to 10-period moving average)
        df['rel_volume'] = df[volume_col] / df[volume_col].rolling(10).mean()
        
        # Candle body size
        df['body_size'] = abs(df[close_col] - df[open_col])
        
        # Candle wick sizes
        df['upper_wick'] = df[high_col] - df[[open_col, close_col]].max(axis=1)
        df['lower_wick'] = df[[open_col, close_col]].min(axis=1) - df[low_col]
        
        # Candle range
        df['range'] = df[high_col] - df[low_col]
        
        # Trend indicators (based on EMA crossovers)
        if 'ema_10' in df.columns and 'ema_20' in df.columns:
            df['trend_ema_10_20'] = (df['ema_10'] > df['ema_20']).astype(int) * 2 - 1  # 1 for uptrend, -1 for downtrend
        
        return df
