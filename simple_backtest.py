"""
Simple backtest script for the DexApp trading bot

This script loads a trained model, performs a backtest on a specific CSV file,
and displays a detailed analysis with charts showing when trades were made.

Usage:
    python simple_backtest.py --model_file dexapp/models/ppo_model.zip --data_file your_file.csv
"""

import os
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from typing import Optional, List, Dict, Union, Tuple, Any

from dexapp.utils.data_processor import DataProcessor
from dexapp.indicators.technical import TechnicalIndicators
from dexapp.environment.trading_env import TradingEnv
from dexapp.agents.rl_agent import PPOAgent, DQNAgent, A2CAgent


def main():
    """
    Main function
    """
    # Parse arguments
    parser = argparse.ArgumentParser(description='Simple Backtest for DexApp Trading Bot')

    # Model arguments
    parser.add_argument('--model_file', type=str, default='dexapp/models/ppo_model.zip',
                       help='Path to the trained model file')
    parser.add_argument('--agent', type=str, choices=['ppo', 'dqn', 'a2c'], default='ppo',
                       help='RL agent type used for the model')

    # Data arguments
    parser.add_argument('--data_file', type=str, required=True,
                       help='Specific data file to use for backtesting')

    # Environment arguments
    parser.add_argument('--min_trade_timeout', type=int, default=10,
                       help='Minimum number of steps between trades (0 = no timeout)')

    args = parser.parse_args()

    # Create output directory if it doesn't exist
    os.makedirs('dexapp/results', exist_ok=True)

    # Load and preprocess data
    data_processor = DataProcessor(data_dir='input')
    df = data_processor.load_data(args.data_file)

    print(f"Loaded data with shape: {df.shape}")
    print(df.head())

    # Add technical indicators
    df = TechnicalIndicators.add_all_indicators(df)

    # Drop rows with NaN values
    df.dropna(inplace=True)

    print(f"Processed data with shape: {df.shape}")

    # Create environment
    env = TradingEnv(
        df=df,
        window_size=20,
        initial_balance=1000.0,
        transaction_fee=0.001,
        min_trade_timeout=args.min_trade_timeout
    )

    # Create agent based on type
    if args.agent == 'ppo':
        agent = PPOAgent(env)
    elif args.agent == 'dqn':
        agent = DQNAgent(env)
    elif args.agent == 'a2c':
        agent = A2CAgent(env)
    else:
        raise ValueError(f"Unknown agent type: {args.agent}")

    # Load the trained model
    model_path = args.model_file

    # Check if the file exists
    if not os.path.exists(model_path):
        # Try without .zip extension
        if model_path.endswith('.zip'):
            model_path = model_path[:-4]
        # Try with .zip extension
        else:
            model_path = f"{model_path}.zip"

    # Check if the file exists now
    if not os.path.exists(model_path):
        print(f"Error: Model file not found at {args.model_file} or {model_path}")
        print("Please train a model first using:")
        print("python -m dexapp.main --mode train --agent ppo --timesteps 10000")
        return

    model_name = os.path.splitext(os.path.basename(model_path))[0]
    print(f"Loading model from {model_name}")
    agent.load(model_name)
    print(f"Loaded model {model_name}")

    # Run backtest
    results_df = agent.backtest(env)

    # Extract trades from the environment
    trades = env.trades

    # Create a DataFrame for trades
    if trades:
        trades_df = pd.DataFrame(trades)
        print(f"\nTotal trades: {len(trades_df)}")
        print(f"Buy trades: {len(trades_df[trades_df['type'] == 'buy'])}")
        print(f"Sell trades: {len(trades_df[trades_df['type'] == 'sell'])}")

        # Calculate profit statistics if we have sell trades with profit
        if 'profit' in trades_df.columns and len(trades_df[trades_df['type'] == 'sell']) > 0:
            sell_trades = trades_df[trades_df['type'] == 'sell']
            total_profit = sell_trades['profit'].sum()
            avg_profit = sell_trades['profit'].mean()
            win_rate = len(sell_trades[sell_trades['profit'] > 0]) / len(sell_trades) * 100

            print(f"\nTotal profit: ${total_profit:.2f}")
            print(f"Average profit per trade: ${avg_profit:.2f}")
            print(f"Win rate: {win_rate:.2f}%")
    else:
        trades_df = pd.DataFrame()
        print("\nNo trades were executed during the backtest")

    # Calculate performance metrics
    initial_balance = 1000.0
    final_balance = results_df.iloc[-1]['portfolio_value'] if not results_df.empty else initial_balance
    total_return = (final_balance - initial_balance) / initial_balance

    print(f"\nBacktest Results:")
    print(f"Initial Balance: ${initial_balance:.2f}")
    print(f"Final Balance: ${final_balance:.2f}")
    print(f"Total Return: {total_return:.2%}")

    # Create visualizations
    create_visualizations(df, results_df, trades_df, args.data_file)


def create_visualizations(data_df, results_df, trades_df, data_file):
    """
    Create visualizations for backtest results
    """
    # Set up the figure
    plt.figure(figsize=(15, 10))

    # 1. Price chart with buy/sell signals
    ax1 = plt.subplot(2, 1, 1)

    # Plot price
    if 'datetime' in results_df.columns:
        x = pd.to_datetime(results_df['datetime'])
    else:
        x = results_df['step']

    # Plot price
    ax1.plot(x, results_df['price'], label='Price', color='blue', alpha=0.7)

    # Plot buy/sell signals if we have trades
    if not trades_df.empty:
        # Get buy trades
        buy_trades = trades_df[trades_df['type'] == 'buy']
        if not buy_trades.empty:
            if 'datetime' in buy_trades.columns:
                buy_x = pd.to_datetime(buy_trades['datetime'])
            else:
                buy_x = [results_df.iloc[step]['step'] for step in buy_trades['step']]

            buy_y = buy_trades['price']
            ax1.scatter(buy_x, buy_y, color='green', marker='^', s=100, label='Buy')

        # Get sell trades
        sell_trades = trades_df[trades_df['type'] == 'sell']
        if not sell_trades.empty:
            if 'datetime' in sell_trades.columns:
                sell_x = pd.to_datetime(sell_trades['datetime'])
            else:
                sell_x = [results_df.iloc[step]['step'] for step in sell_trades['step']]

            sell_y = sell_trades['price']
            ax1.scatter(sell_x, sell_y, color='red', marker='v', s=100, label='Sell')

    ax1.set_title('Price Chart with Buy/Sell Signals')
    ax1.set_ylabel('Price')
    ax1.grid(True)
    ax1.legend()

    # 2. Portfolio value
    ax2 = plt.subplot(2, 1, 2, sharex=ax1)
    ax2.plot(x, results_df['portfolio_value'], label='Portfolio Value', color='purple')
    ax2.set_title('Portfolio Value')
    ax2.set_ylabel('Value ($)')
    ax2.grid(True)

    # Format x-axis
    if 'datetime' in results_df.columns:
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d %H:%M'))
        plt.xticks(rotation=45)

    plt.tight_layout()

    # Save the plot
    plot_file = os.path.join('dexapp/results', f"backtest_plot_{os.path.basename(data_file).split('.')[0]}.png")
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    print(f"Saved plot to {plot_file}")

    plt.show()

    # If we have trades, create a trade analysis plot
    if not trades_df.empty and 'profit' in trades_df.columns and len(trades_df[trades_df['type'] == 'sell']) > 0:
        plt.figure(figsize=(15, 7))

        # Trade profit histogram
        ax1 = plt.subplot(1, 2, 1)
        sell_trades = trades_df[trades_df['type'] == 'sell']
        sell_trades['profit'].hist(bins=20, ax=ax1, color='skyblue', edgecolor='black')
        ax1.axvline(0, color='red', linestyle='--')
        ax1.set_title('Trade Profit Distribution')
        ax1.set_xlabel('Profit ($)')
        ax1.set_ylabel('Frequency')

        # Cumulative profit
        ax2 = plt.subplot(1, 2, 2)
        cumulative_profit = sell_trades['profit'].cumsum()
        ax2.plot(cumulative_profit.index, cumulative_profit, color='green')
        ax2.set_title('Cumulative Profit')
        ax2.set_xlabel('Trade Number')
        ax2.set_ylabel('Cumulative Profit ($)')
        ax2.grid(True)

        plt.tight_layout()

        # Save the plot
        plot_file = os.path.join('dexapp/results', f"trade_analysis_{os.path.basename(data_file).split('.')[0]}.png")
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        print(f"Saved trade analysis plot to {plot_file}")

        plt.show()


if __name__ == "__main__":
    main()
