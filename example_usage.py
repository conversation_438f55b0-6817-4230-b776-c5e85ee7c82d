"""
Example usage of the DexApp trading bot
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from dexapp.utils.data_processor import DataProcessor
from dexapp.indicators.technical import TechnicalIndicators
from dexapp.environment.trading_env import TradingEnv
from dexapp.agents.rl_agent import PPOAgent

# Load data from all CSV files in the input directory
data_processor = DataProcessor(data_dir="input")
df = data_processor.load_data()  # No file_path means load all CSV files
print(f"Loaded data with shape: {df.shape}")
print(df.head())

# Since we have very limited data, let's generate synthetic data for demonstration
print("\nGenerating synthetic data for demonstration...")
# Start with the last row of our real data
last_row = df.iloc[-1].copy()
start_price = last_row['c']
start_time = df.index[-1]

# Generate 500 more rows with random price movements
synthetic_data = []
current_price = start_price
current_time = start_time

for i in range(500):
    # Move time forward by 1 minute
    current_time = current_time + pd.Timedelta(minutes=1)

    # Generate random price movement (between -2% and +2%)
    price_change = np.random.uniform(-0.02, 0.02)
    current_price = current_price * (1 + price_change)

    # Generate high, low, open prices
    high_price = current_price * (1 + np.random.uniform(0, 0.01))
    low_price = current_price * (1 - np.random.uniform(0, 0.01))
    open_price = current_price * (1 + np.random.uniform(-0.01, 0.01))

    # Generate random volume
    volume = np.random.uniform(1e8, 5e8)

    # Create a new row
    new_row = last_row.copy()
    new_row['c'] = current_price
    new_row['h'] = high_price
    new_row['l'] = low_price
    new_row['o'] = open_price
    new_row['v'] = volume
    new_row['unixTime'] = int(current_time.timestamp())

    synthetic_data.append(new_row)

# Create a DataFrame from synthetic data
synthetic_df = pd.DataFrame(synthetic_data)
synthetic_df.index = pd.DatetimeIndex([current_time for current_time in pd.date_range(start=start_time + pd.Timedelta(minutes=1), periods=500, freq='1min')])

# Combine real and synthetic data
combined_df = pd.concat([df, synthetic_df])
print(f"Combined data with shape: {combined_df.shape}")
print(combined_df.head())
print(combined_df.tail())

# Add technical indicators
df_with_indicators = TechnicalIndicators.add_all_indicators(combined_df)

# Drop rows with NaN values (first rows will have NaN due to indicators calculation)
df_with_indicators.dropna(inplace=True)
print(f"\nProcessed data with shape: {df_with_indicators.shape}")
print(df_with_indicators.head())

# Visualize some indicators
plt.figure(figsize=(12, 8))
plt.subplot(3, 1, 1)
plt.plot(df_with_indicators.index, df_with_indicators['c'])
plt.title('Close Price')
plt.grid(True)

plt.subplot(3, 1, 2)
plt.plot(df_with_indicators.index, df_with_indicators['rsi'])
plt.title('RSI')
plt.grid(True)

plt.subplot(3, 1, 3)
plt.plot(df_with_indicators.index, df_with_indicators['macd'])
plt.plot(df_with_indicators.index, df_with_indicators['macd_signal'])
plt.title('MACD')
plt.grid(True)

plt.tight_layout()
plt.savefig('indicators_plot.png')
print("Saved indicators plot to indicators_plot.png")

# Split data
train_df, val_df, test_df = data_processor.split_data(df_with_indicators)
print(f"\nTrain data shape: {train_df.shape}")
print(f"Validation data shape: {val_df.shape}")
print(f"Test data shape: {test_df.shape}")

# Check if we have enough data for the environment
if len(train_df) > 20:  # Minimum window size
    # Create environment
    env = TradingEnv(
        df=train_df,
        window_size=20,
        initial_balance=1000.0,
        transaction_fee=0.001
    )

    print("\nEnvironment created successfully!")
    print("Example of observation shape:", env.reset().shape)
    print("Action space:", env.action_space)

    # Take a few random actions
    print("\nTaking a few random actions:")
    for i in range(5):
        action = env.action_space.sample()
        obs, reward, done, info = env.step(action)
        print(f"Action: {action}, Reward: {reward:.4f}, Done: {done}")
        print(f"Portfolio Value: ${info['portfolio_value']:.2f}")
        print("-" * 30)

    print("\nTo train and test the agent, run:")
    print("python -m dexapp.main --mode train --agent ppo --timesteps 10000")
    print("python -m dexapp.main --mode test --agent ppo")
else:
    print("\nNot enough data for the environment after preprocessing.")
    print("In a real scenario, you would need more historical data.")

print("\nExample script completed successfully!")
