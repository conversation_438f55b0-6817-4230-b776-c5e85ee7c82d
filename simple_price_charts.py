"""
<PERSON><PERSON><PERSON> to generate simple price charts for all input data files

This script:
1. Reads all CSV files from the 'input' directory
2. Generates simple price charts for each file
3. Saves the charts in a 'price_charts' directory
"""

import os
import glob
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from dexapp.utils.data_processor import DataProcessor


def main():
    """
    Main function to generate simple price charts
    """
    # Create output directory if it doesn't exist
    output_dir = 'price_charts'
    os.makedirs(output_dir, exist_ok=True)
    
    # Get all CSV files in the input directory
    input_dir = 'input'
    csv_files = glob.glob(os.path.join(input_dir, "*.csv"))
    
    if not csv_files:
        print(f"No CSV files found in {input_dir} directory.")
        return
    
    print(f"Found {len(csv_files)} CSV files in {input_dir} directory.")
    
    # Initialize data processor
    data_processor = DataProcessor(data_dir=input_dir)
    
    # Process each file
    for file_path in csv_files:
        file_name = os.path.basename(file_path)
        print(f"Processing {file_name}...")
        
        try:
            # Load data
            df = data_processor.load_data(file_name)
            
            # Generate simple price chart
            generate_simple_price_chart(df, file_name, output_dir)
            
        except Exception as e:
            print(f"Error processing {file_name}: {e}")


def generate_simple_price_chart(df, file_name, output_dir):
    """
    Generate a simple price chart for the given DataFrame
    
    Args:
        df: DataFrame with price data
        file_name: Name of the data file
        output_dir: Directory to save charts
    """
    # Create figure
    plt.figure(figsize=(12, 6))
    
    # Plot price
    plt.plot(df.index, df['c'], color='blue')
    
    # Format x-axis dates
    plt.gcf().autofmt_xdate()
    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    
    # Add grid and labels
    plt.grid(True, alpha=0.3)
    plt.title(f'Price Chart - {os.path.splitext(file_name)[0]}')
    plt.xlabel('Date')
    plt.ylabel('Price')
    
    # Save chart
    output_file = os.path.join(output_dir, f"{os.path.splitext(file_name)[0]}_price.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Saved price chart to {output_file}")
    
    # Close figure to free memory
    plt.close()


if __name__ == "__main__":
    main()
