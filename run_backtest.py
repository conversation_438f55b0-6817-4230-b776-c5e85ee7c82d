"""
Simple script to run a backtest analysis on a specific file

Usage:
    python run_backtest.py --model_file dexapp/models/ppo_model.zip --data_file your_file.csv
"""

import argparse
import os
from backtest_analysis import run_backtest


def parse_args():
    """
    Parse command line arguments

    Returns:
        Parsed arguments
    """
    parser = argparse.ArgumentParser(description='Run Backtest Analysis')

    # Model arguments
    parser.add_argument('--model_file', type=str, default='dexapp/models/ppo_model',
                       help='Path to the trained model file')
    parser.add_argument('--agent', type=str, choices=['ppo', 'dqn', 'a2c'], default='ppo',
                       help='RL agent type used for the model')

    # Data arguments
    parser.add_argument('--data_dir', type=str, default='input',
                       help='Directory containing data files')
    parser.add_argument('--data_file', type=str, required=True,
                       help='Specific data file to use for backtesting')

    # Environment arguments
    parser.add_argument('--window_size', type=int, default=20,
                       help='Window size for observations')
    parser.add_argument('--initial_balance', type=float, default=1000.0,
                       help='Initial balance for trading')
    parser.add_argument('--transaction_fee', type=float, default=0.001,
                       help='Transaction fee as a percentage')

    # Output arguments
    parser.add_argument('--output_dir', type=str, default='dexapp/results',
                       help='Directory to save results')
    parser.add_argument('--save_plots', action='store_true', default=True,
                       help='Save plots to output directory')

    return parser.parse_args()


def main():
    """
    Main function
    """
    args = parse_args()

    # Check if the model file exists
    model_path = args.model_file
    if not os.path.exists(model_path):
        # Try with .zip extension if not already present
        if not model_path.endswith('.zip'):
            model_path = f"{model_path}"

        # Check again
        if not os.path.exists(model_path):
            print(f"Error: Model file '{args.model_file}' not found.")
            print("Please train a model first using:")
            print("python -m dexapp.main --mode train --agent ppo --timesteps 10000")
            return

    # Update the model file path
    args.model_file = model_path

    # Check if the data file exists
    data_path = os.path.join(args.data_dir, args.data_file)
    if not os.path.exists(data_path):
        print(f"Error: Data file '{data_path}' not found.")
        print(f"Please make sure the file exists in the '{args.data_dir}' directory.")
        return

    # Run the backtest
    run_backtest(args)


if __name__ == "__main__":
    main()
