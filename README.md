# DexApp - Memecoin Trading Bot

A reinforcement learning-based trading bot for short-term memecoin trading, designed to achieve small but stable profits with minimal risk.

## Project Structure

The project is organized into the following modules:

- **Data Processing**: Loading and preprocessing trading data
- **Technical Indicators**: Calculating technical indicators for trading analysis
- **RL Environment**: Custom OpenAI Gym environment for trading simulation
- **RL Agents**: Implementation of various RL algorithms for trading
- **Main Controller**: Coordinating all components and running experiments

## Features

- **Data Processing**:
  - Loading and preprocessing OHLCV data
  - Data visualization
  - Train/validation/test splitting

- **Technical Indicators**:
  - RSI (Relative Strength Index)
  - MACD (Moving Average Convergence Divergence)
  - Bollinger Bands
  - EMA (Exponential Moving Average)
  - Stochastic Oscillator
  - Additional price-based features

- **RL Environment**:
  - Custom OpenAI Gym environment for trading
  - Realistic trading simulation with transaction fees
  - Flexible state representation
  - Customizable reward function

- **RL Agents**:
  - PPO (Proximal Policy Optimization)
  - DQN (Deep Q-Network)
  - A2C (Advantage Actor-Critic)
  - Model saving and loading
  - Backtesting functionality

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/dexapp.git
cd dexapp
```

2. Install the required packages:
```bash
pip install -r requirements.txt
```

## Usage

### Example Usage

```python
from dexapp.utils.data_processor import DataProcessor
from dexapp.indicators.technical import TechnicalIndicators
from dexapp.environment.trading_env import TradingEnv
from dexapp.agents.rl_agent import PPOAgent

# Load and preprocess data
data_processor = DataProcessor(data_dir="example")
df = data_processor.load_data("your_data_file.csv")

# Add technical indicators
df = TechnicalIndicators.add_all_indicators(df)

# Create environment
env = TradingEnv(df=df, window_size=20, initial_balance=1000.0)

# Create and train agent
agent = PPOAgent(env)
agent.train(total_timesteps=100000)
agent.save("ppo_model.zip")

# Backtest the agent
results = agent.backtest(env)
agent.plot_results(results)
```

### Command Line Interface

The project provides a command-line interface for training and testing:

```bash
# Train a PPO agent
python -m dexapp.main --mode train --agent ppo --timesteps 100000

# Test a trained agent
python -m dexapp.main --mode test --agent ppo

# Backtest a trained agent on the entire dataset
python -m dexapp.main --mode backtest --agent ppo
```

## Configuration

You can configure the following parameters:

- **Data**:
  - `--data_dir`: Directory containing data files
  - `--data_file`: Data file to use

- **Training**:
  - `--mode`: Mode to run in (train, test, backtest)
  - `--agent`: RL agent to use (ppo, dqn, a2c)
  - `--timesteps`: Number of timesteps to train for
  - `--window_size`: Window size for observations

- **Environment**:
  - `--initial_balance`: Initial balance for trading
  - `--transaction_fee`: Transaction fee as a percentage

- **Model**:
  - `--model_dir`: Directory to save/load models
  - `--model_file`: Model file to load (for test/backtest mode)

- **Output**:
  - `--results_dir`: Directory to save results
  - `--verbose`: Verbosity level

## License

This project is licensed under the MIT License - see the LICENSE file for details.
