"""
Main controller script for the DexApp trading bot
"""

import os
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Optional, List, Dict, Union, Tuple, Any

from dexapp.utils.data_processor import DataProcessor
from dexapp.indicators.technical import TechnicalIndicators
from dexapp.environment.trading_env import TradingEnv
from dexapp.agents.rl_agent import PPOAgent, DQNAgent, A2CAgent


def parse_args():
    """
    Parse command line arguments

    Returns:
        Parsed arguments
    """
    parser = argparse.ArgumentParser(description='DexApp Trading Bot')

    # Data arguments
    parser.add_argument('--data_dir', type=str, default='input',
                       help='Directory containing data files')
    parser.add_argument('--data_file', type=str, default=None,
                       help='Specific data file to use. If None, use all CSV files in data_dir')

    # Training arguments
    parser.add_argument('--mode', type=str, choices=['train', 'test', 'backtest'], default='train',
                       help='Mode to run in')
    parser.add_argument('--agent', type=str, choices=['ppo', 'dqn', 'a2c'], default='ppo',
                       help='RL agent to use')
    parser.add_argument('--timesteps', type=int, default=100000,
                       help='Number of timesteps to train for')
    parser.add_argument('--window_size', type=int, default=20,
                       help='Window size for observations')

    # Environment arguments
    parser.add_argument('--initial_balance', type=float, default=1000.0,
                       help='Initial balance for trading')
    parser.add_argument('--transaction_fee', type=float, default=0.001,
                       help='Transaction fee as a percentage')
    parser.add_argument('--min_trade_timeout', type=int, default=10,
                       help='Minimum number of steps between trades (0 = no timeout)')

    # Model arguments
    parser.add_argument('--model_dir', type=str, default='dexapp/models',
                       help='Directory to save/load models')
    parser.add_argument('--model_file', type=str, default=None,
                       help='Model file to load (for test/backtest mode)')

    # Output arguments
    parser.add_argument('--results_dir', type=str, default='dexapp/results',
                       help='Directory to save results')
    parser.add_argument('--verbose', type=int, default=1,
                       help='Verbosity level')

    return parser.parse_args()


def main():
    """
    Main function
    """
    # Parse arguments
    args = parse_args()

    # Create directories if they don't exist
    os.makedirs(args.model_dir, exist_ok=True)
    os.makedirs(args.results_dir, exist_ok=True)

    # Load and preprocess data
    data_processor = DataProcessor(data_dir=args.data_dir)
    df = data_processor.load_data(args.data_file)

    if args.verbose > 0:
        print(f"Loaded data with shape: {df.shape}")
        print(df.head())

    # Add technical indicators
    df = TechnicalIndicators.add_all_indicators(df)

    # Drop rows with NaN values
    df.dropna(inplace=True)

    if args.verbose > 0:
        print(f"Processed data with shape: {df.shape}")
        print(df.head())

    # Split data
    train_df, val_df, test_df = data_processor.split_data(df)

    if args.verbose > 0:
        print(f"Train data shape: {train_df.shape}")
        print(f"Validation data shape: {val_df.shape}")
        print(f"Test data shape: {test_df.shape}")

    # Create environments
    train_env = TradingEnv(
        df=train_df,
        window_size=args.window_size,
        initial_balance=args.initial_balance,
        transaction_fee=args.transaction_fee,
        min_trade_timeout=args.min_trade_timeout
    )

    val_env = TradingEnv(
        df=val_df,
        window_size=args.window_size,
        initial_balance=args.initial_balance,
        transaction_fee=args.transaction_fee,
        min_trade_timeout=args.min_trade_timeout
    )

    test_env = TradingEnv(
        df=test_df,
        window_size=args.window_size,
        initial_balance=args.initial_balance,
        transaction_fee=args.transaction_fee,
        min_trade_timeout=args.min_trade_timeout
    )

    # Create agent
    if args.agent == 'ppo':
        agent = PPOAgent(train_env, model_dir=args.model_dir)
    elif args.agent == 'dqn':
        agent = DQNAgent(train_env, model_dir=args.model_dir)
    elif args.agent == 'a2c':
        agent = A2CAgent(train_env, model_dir=args.model_dir)
    else:
        raise ValueError(f"Unknown agent: {args.agent}")

    # Run in specified mode
    if args.mode == 'train':
        # Train the agent
        agent.train(total_timesteps=args.timesteps)

        # Save the model
        model_file = f"{args.agent}_model.zip"
        agent.save(model_file)

        # Evaluate on validation data
        val_mean_reward, val_std_reward = agent.evaluate(env=val_env)

        # Backtest on validation data
        val_results = agent.backtest(env=val_env)

        # Save validation results
        val_results.to_csv(os.path.join(args.results_dir, f"{args.agent}_val_results.csv"), index=False)

        # Plot validation results
        agent.plot_results(val_results)

    elif args.mode == 'test':
        # Load the model
        if args.model_file is None:
            model_file = f"{args.agent}_model.zip"
        else:
            model_file = args.model_file

        agent.load(model_file)

        # Evaluate on test data
        test_mean_reward, test_std_reward = agent.evaluate(env=test_env)

        # Backtest on test data
        test_results = agent.backtest(env=test_env)

        # Save test results
        test_results.to_csv(os.path.join(args.results_dir, f"{args.agent}_test_results.csv"), index=False)

        # Plot test results
        agent.plot_results(test_results)

    elif args.mode == 'backtest':
        # Load the model
        if args.model_file is None:
            model_file = f"{args.agent}_model.zip"
        else:
            model_file = args.model_file

        agent.load(model_file)

        # Backtest on the entire dataset
        full_env = TradingEnv(
            df=df,
            window_size=args.window_size,
            initial_balance=args.initial_balance,
            transaction_fee=args.transaction_fee,
            min_trade_timeout=args.min_trade_timeout
        )

        full_results = agent.backtest(env=full_env)

        # Save full results
        full_results.to_csv(os.path.join(args.results_dir, f"{args.agent}_full_results.csv"), index=False)

        # Plot full results
        agent.plot_results(full_results)


if __name__ == "__main__":
    main()
