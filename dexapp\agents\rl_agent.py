"""
Reinforcement Learning agents for trading
"""

import os
import numpy as np
import pandas as pd
from typing import Optional, List, Dict, Union, Tuple, Any
from stable_baselines3 import PPO, A2C, DQN
from stable_baselines3.common.callbacks import BaseCallback, EvalCallback
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.evaluation import evaluate_policy
import matplotlib.pyplot as plt


class TradingAgent:
    """
    Base class for RL trading agents
    """

    def __init__(self, env, model_dir: str = "dexapp/models"):
        """
        Initialize the trading agent

        Args:
            env: Trading environment
            model_dir: Directory to save models
        """
        self.env = env
        self.model_dir = model_dir
        self.model = None

        # Create model directory if it doesn't exist
        os.makedirs(model_dir, exist_ok=True)

    def train(self, total_timesteps: int = 100000, log_interval: int = 1000):
        """
        Train the agent

        Args:
            total_timesteps: Total number of timesteps to train for
            log_interval: Interval for logging

        Returns:
            Trained model
        """
        raise NotImplementedError("Subclasses must implement train method")

    def save(self, filename: str):
        """
        Save the model

        Args:
            filename: Filename to save the model to
        """
        if self.model is None:
            raise ValueError("Model not initialized. Call train first.")

        path = os.path.join(self.model_dir, filename)
        self.model.save(path)
        print(f"Model saved to {path}")

    def load(self, filename: str):
        """
        Load the model

        Args:
            filename: Filename to load the model from
        """
        raise NotImplementedError("Subclasses must implement load method")

    def evaluate(self, env=None, n_eval_episodes: int = 10):
        """
        Evaluate the agent

        Args:
            env: Environment to evaluate on. If None, use self.env
            n_eval_episodes: Number of episodes to evaluate for

        Returns:
            Mean reward and standard deviation
        """
        if self.model is None:
            raise ValueError("Model not initialized. Call train or load first.")

        if env is None:
            env = self.env

        mean_reward, std_reward = evaluate_policy(
            self.model, env, n_eval_episodes=n_eval_episodes
        )

        print(f"Mean reward: {mean_reward:.2f} +/- {std_reward:.2f}")
        return mean_reward, std_reward

    def backtest(self, env=None, render: bool = False):
        """
        Backtest the agent on historical data

        Args:
            env: Environment to backtest on. If None, use self.env
            render: Whether to render the environment during backtesting

        Returns:
            DataFrame with backtest results
        """
        if self.model is None:
            raise ValueError("Model not initialized. Call train or load first.")

        if env is None:
            env = self.env

        # Reset the environment
        obs = env.reset()
        done = False

        # Initialize results
        results = []

        # Run the backtest
        while not done:
            # Get action from model
            action, _ = self.model.predict(obs, deterministic=True)

            # Take step in environment
            obs, reward, done, info = env.step(action)

            # Store results
            results.append(info)

            # Render if requested
            if render:
                env.render()

        # Convert results to DataFrame
        results_df = pd.DataFrame(results)

        # Calculate additional metrics
        if len(results_df) > 0:
            # Calculate returns
            initial_value = env.initial_balance
            final_value = results_df.iloc[-1]['portfolio_value']
            total_return = (final_value - initial_value) / initial_value

            # Calculate Sharpe ratio (assuming risk-free rate of 0)
            if 'portfolio_value' in results_df.columns:
                portfolio_values = results_df['portfolio_value']
                daily_returns = portfolio_values.pct_change().dropna()
                sharpe_ratio = np.sqrt(252) * daily_returns.mean() / daily_returns.std() if daily_returns.std() != 0 else 0
            else:
                sharpe_ratio = 0

            print(f"Backtest Results:")
            print(f"Initial Value: ${initial_value:.2f}")
            print(f"Final Value: ${final_value:.2f}")
            print(f"Total Return: {total_return:.2%}")
            print(f"Sharpe Ratio: {sharpe_ratio:.2f}")

            # Add metrics to results
            results_df['total_return'] = total_return
            results_df['sharpe_ratio'] = sharpe_ratio

        return results_df

    def plot_results(self, results_df: pd.DataFrame, figsize: Tuple[int, int] = (12, 8)):
        """
        Plot backtest results

        Args:
            results_df: DataFrame with backtest results
            figsize: Figure size
        """
        if len(results_df) == 0:
            print("No results to plot")
            return

        fig, axes = plt.subplots(3, 1, figsize=figsize, sharex=True)

        # Plot portfolio value
        if 'portfolio_value' in results_df.columns:
            axes[0].plot(results_df['step'], results_df['portfolio_value'])
            axes[0].set_title('Portfolio Value')
            axes[0].grid(True)

        # Plot price
        if 'price' in results_df.columns:
            axes[1].plot(results_df['step'], results_df['price'])
            axes[1].set_title('Price')
            axes[1].grid(True)

        # Plot holdings value
        if 'holdings_value' in results_df.columns:
            axes[2].plot(results_df['step'], results_df['holdings_value'])
            axes[2].set_title('Holdings Value')
            axes[2].grid(True)

        plt.tight_layout()
        plt.show()


class PPOAgent(TradingAgent):
    """
    PPO agent for trading
    """

    def __init__(self, env, model_dir: str = "dexapp/models", **kwargs):
        """
        Initialize the PPO agent

        Args:
            env: Trading environment
            model_dir: Directory to save models
            **kwargs: Additional arguments for PPO
        """
        super(PPOAgent, self).__init__(env, model_dir)

        # Default PPO parameters - optimized to discourage immediate buying
        self.params = {
            'policy': 'MlpPolicy',
            'learning_rate': 3e-4,  # Increased learning rate
            'n_steps': 512,         # Reduced steps for faster updates
            'batch_size': 64,
            'n_epochs': 5,          # Reduced epochs per update
            'gamma': 0.99,
            'gae_lambda': 0.95,
            'clip_range': 0.2,
            'ent_coef': 0.05,       # Increased entropy coefficient for more exploration
            'verbose': 1,
            'policy_kwargs': {'net_arch': [64, 64]}  # Smaller network
        }

        # Update with provided parameters
        self.params.update(kwargs)

    def train(self, total_timesteps: int = 100000, log_interval: int = 1000):
        """
        Train the PPO agent

        Args:
            total_timesteps: Total number of timesteps to train for
            log_interval: Interval for logging

        Returns:
            Trained model
        """
        # Initialize the model
        self.model = PPO(env=self.env, **self.params)

        # Train the model
        self.model.learn(total_timesteps=total_timesteps, log_interval=log_interval)

        return self.model

    def load(self, filename: str):
        """
        Load the PPO model

        Args:
            filename: Filename to load the model from
        """
        path = os.path.join(self.model_dir, filename)
        self.model = PPO.load(path, env=self.env)
        print(f"Model loaded from {path}")
        return self.model


class DQNAgent(TradingAgent):
    """
    DQN agent for trading
    """

    def __init__(self, env, model_dir: str = "dexapp/models", **kwargs):
        """
        Initialize the DQN agent

        Args:
            env: Trading environment
            model_dir: Directory to save models
            **kwargs: Additional arguments for DQN
        """
        super(DQNAgent, self).__init__(env, model_dir)

        # Default DQN parameters - optimized to discourage immediate buying
        self.params = {
            'policy': 'MlpPolicy',
            'learning_rate': 5e-4,         # Increased learning rate
            'buffer_size': 5000,           # Smaller buffer for faster updates
            'learning_starts': 200,        # Start learning earlier
            'batch_size': 64,
            'gamma': 0.99,
            'target_update_interval': 100, # More frequent target updates
            'train_freq': 4,               # Train every 4 steps
            'gradient_steps': 1,
            'exploration_fraction': 0.3,   # Longer exploration phase
            'exploration_initial_eps': 1.0,
            'exploration_final_eps': 0.1,  # Higher final exploration rate
            'verbose': 1,
            'policy_kwargs': {'net_arch': [64, 64]}  # Smaller network
        }

        # Update with provided parameters
        self.params.update(kwargs)

    def train(self, total_timesteps: int = 100000, log_interval: int = 1000):
        """
        Train the DQN agent

        Args:
            total_timesteps: Total number of timesteps to train for
            log_interval: Interval for logging

        Returns:
            Trained model
        """
        # Initialize the model
        self.model = DQN(env=self.env, **self.params)

        # Train the model
        self.model.learn(total_timesteps=total_timesteps, log_interval=log_interval)

        return self.model

    def load(self, filename: str):
        """
        Load the DQN model

        Args:
            filename: Filename to load the model from
        """
        path = os.path.join(self.model_dir, filename)
        self.model = DQN.load(path, env=self.env)
        print(f"Model loaded from {path}")
        return self.model


class A2CAgent(TradingAgent):
    """
    A2C agent for trading
    """

    def __init__(self, env, model_dir: str = "dexapp/models", **kwargs):
        """
        Initialize the A2C agent

        Args:
            env: Trading environment
            model_dir: Directory to save models
            **kwargs: Additional arguments for A2C
        """
        super(A2CAgent, self).__init__(env, model_dir)

        # Default A2C parameters
        self.params = {
            'policy': 'MlpPolicy',
            'learning_rate': 1e-4,
            'n_steps': 5,
            'gamma': 0.99,
            'gae_lambda': 0.95,
            'ent_coef': 0.01,
            'vf_coef': 0.5,
            'max_grad_norm': 0.5,
            'rms_prop_eps': 1e-5,
            'use_rms_prop': True,
            'normalize_advantage': True,
            'verbose': 1
        }

        # Update with provided parameters
        self.params.update(kwargs)

    def train(self, total_timesteps: int = 100000, log_interval: int = 1000):
        """
        Train the A2C agent

        Args:
            total_timesteps: Total number of timesteps to train for
            log_interval: Interval for logging

        Returns:
            Trained model
        """
        # Initialize the model
        self.model = A2C(env=self.env, **self.params)

        # Train the model
        self.model.learn(total_timesteps=total_timesteps, log_interval=log_interval)

        return self.model

    def load(self, filename: str):
        """
        Load the A2C model

        Args:
            filename: Filename to load the model from
        """
        path = os.path.join(self.model_dir, filename)
        self.model = A2C.load(path, env=self.env)
        print(f"Model loaded from {path}")
        return self.model
